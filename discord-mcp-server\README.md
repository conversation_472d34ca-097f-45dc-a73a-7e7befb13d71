# Discord MCP Server

A production-ready Model Context Protocol (MCP) server that enables AI models to interact with Discord through secure, authenticated tools. Built for the W4D3 Understanding RAG assignment.

## 🚀 Features

### Core Discord Tools
- **`send_message`** - Send messages to Discord channels with optional embeds
- **`get_messages`** - Retrieve message history with pagination
- **`get_channel_info`** - Fetch channel metadata and information
- **`search_messages`** - Search through messages with advanced filters
- **`moderate_content`** - Perform moderation actions (delete, timeout, ban, kick)

### Security & Authentication
- 🔐 **API Key Authentication** - Secure hashing and validation
- 👥 **Multi-tenancy Support** - Database-backed tenant isolation
- 🛡️ **Permission System** - Granular RBAC per tenant
- 📊 **Audit Logging** - Complete audit trail with database storage
- ⚡ **Rate Limiting** - Sliding window rate limiting per tenant/tool
- 🔒 **Input Validation** - Comprehensive sanitization and security checks

### Development & Debugging
- 🔍 **MCP Inspector Integration** - Real-time debugging and monitoring
- 📈 **Performance Monitoring** - Request/response tracking with metrics
- 🧪 **Comprehensive Testing** - >80% test coverage with unit/integration tests
- 📝 **Structured Logging** - Winston-based logging with audit trails
- 🛠️ **Environment Validation** - Automated security and configuration checks

## 📋 Prerequisites

- **Node.js** 18+ 
- **Discord Bot Token** - From Discord Developer Portal
- **Discord Application ID** - From Discord Developer Portal

## 🛠️ Installation

1. **Clone and Setup**
   ```bash
   cd discord-mcp-server
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your Discord credentials
   ```

3. **Build the Project**
   ```bash
   npm run build
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Discord Configuration (Required)
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_APPLICATION_ID=your_discord_application_id_here

# Security (Required)
JWT_SECRET=your_jwt_secret_key_here
API_KEYS=api_key_1:tenant_1,api_key_2:tenant_2
ENCRYPTION_KEY=your_32_character_encryption_key

# Optional Configuration
MCP_SERVER_PORT=3000
DATABASE_URL=sqlite:./data/discord_mcp.db
LOG_LEVEL=info
```

### Discord Bot Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to "Bot" section and create a bot
4. Copy the bot token to your `.env` file
5. Enable necessary intents:
   - Message Content Intent
   - Server Members Intent
   - Guild Messages Intent

## 🚀 Usage

### Running the Server

```bash
# Development mode
npm run dev

# Production mode
npm run build
npm start

# With MCP Inspector (debugging)
npm run inspector
```

### Claude Desktop Integration

Add to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "discord": {
      "command": "node",
      "args": ["path/to/discord-mcp-server/dist/index.js"],
      "env": {
        "DISCORD_BOT_TOKEN": "your_token",
        "API_KEYS": "your_api_key:tenant_id"
      }
    }
  }
}
```

### MCP Inspector

Access the debugging interface at `http://localhost:3001` when inspector is enabled.

## 🔧 API Reference

### Tools

#### `send_message`
Send a message to a Discord channel.

```typescript
{
  channel_id: string,
  content: string,
  embed?: {
    title?: string,
    description?: string,
    color?: number,
    fields?: Array<{name: string, value: string, inline?: boolean}>
  }
}
```

#### `get_messages`
Retrieve message history from a channel.

```typescript
{
  channel_id: string,
  limit?: number, // 1-100, default 50
  before?: string, // Message ID
  after?: string   // Message ID
}
```

#### `get_channel_info`
Get information about a Discord channel.

```typescript
{
  channel_id: string
}
```

#### `search_messages`
Search for messages with filters.

```typescript
{
  query: string,
  channel_id?: string,
  author_id?: string,
  date_from?: string,
  date_to?: string,
  limit?: number // 1-50, default 25
}
```

#### `moderate_content`
Perform moderation actions.

```typescript
{
  action: 'delete_message' | 'timeout_user' | 'ban_user' | 'kick_user',
  target_id: string,
  reason?: string,
  duration?: number // For timeouts, in seconds
}
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## 📊 Monitoring

The server provides comprehensive logging and monitoring:

- **Application Logs**: `./logs/discord-mcp-server.log`
- **Error Logs**: `./logs/discord-mcp-server.error.log`
- **Audit Logs**: `./logs/discord-mcp-server.audit.log`
- **Performance Logs**: `./logs/discord-mcp-server.performance.log`

## 🔒 Security

- All API keys are encrypted at rest
- Rate limiting prevents abuse
- Input validation on all endpoints
- Audit logging for security events
- Multi-tenant isolation
- Discord API compliance

## 📝 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

- GitHub Issues: Report bugs and feature requests
- Documentation: Check the `/docs` folder
- MCP Inspector: Use for real-time debugging
