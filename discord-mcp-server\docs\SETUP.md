# Discord MCP Server Setup Guide

## Quick Start (5 Minutes)

### 1. Prerequisites Check

Ensure you have:
- **Node.js 18+** installed
- **Discord account** with developer access
- **Text editor** (VS Code recommended)

### 2. Discord Bot Creation

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click **"New Application"**
3. Name your application (e.g., "My MCP Bot")
4. Go to **"Bot"** section → **"Add Bot"**
5. **Copy the bot token** (keep this secret!)
6. Enable these **Privileged Gateway Intents**:
   - ✅ Message Content Intent
   - ✅ Server Members Intent

### 3. Project Setup

```bash
# Clone the repository
git clone <repository-url>
cd discord-mcp-server

# Install dependencies
npm install

# Copy environment template
cp .env.example .env
```

### 4. Configuration

Edit `.env` file with your Discord credentials:

```env
# Required: Discord Configuration
DISCORD_BOT_TOKEN=your_bot_token_from_step_2
DISCORD_APPLICATION_ID=your_application_id_from_discord_portal

# Required: Security Keys (generate secure random strings)
JWT_SECRET=your_secure_jwt_secret_minimum_32_characters
API_KEYS=my_api_key_123:my_tenant_name
ENCRYPTION_KEY=your_32_character_encryption_key

# Optional: Server Configuration
MCP_SERVER_PORT=3000
LOG_LEVEL=info
```

### 5. Start the Server

```bash
# Development mode (with auto-reload)
npm run dev

# Or production mode
npm run build
npm start
```

### 6. Invite Bot to Discord Server

1. Go back to Discord Developer Portal
2. Go to **"OAuth2"** → **"URL Generator"**
3. Select **"bot"** scope
4. Select these **permissions**:
   - View Channels
   - Send Messages
   - Read Message History
   - Manage Messages
   - Embed Links
   - Kick Members
   - Ban Members
   - Moderate Members
5. **Copy the generated URL** and open it
6. **Select your Discord server** and authorize

### 7. Test the Setup

```bash
# Check if server is running
curl http://localhost:3000/health

# Open MCP Inspector (optional)
open http://localhost:3001
```

## Detailed Setup

### Environment Variables Explained

#### Required Variables

```env
# Discord Bot Token (from Discord Developer Portal)
DISCORD_BOT_TOKEN=MTIzNDU2Nzg5MDEyMzQ1Njc4.GhIjKl.MnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWx

# Discord Application ID (from Discord Developer Portal)
DISCORD_APPLICATION_ID=123456789012345678

# JWT Secret (minimum 32 characters, used for token signing)
JWT_SECRET=super_secure_jwt_secret_key_for_authentication_purposes

# API Keys (format: key:tenant_name, comma-separated for multiple)
API_KEYS=secure_api_key_123:main_tenant,another_key_456:secondary_tenant

# Encryption Key (exactly 32 characters, used for data encryption)
ENCRYPTION_KEY=abcdefghijklmnopqrstuvwxyz123456
```

#### Optional Variables

```env
# Server Configuration
MCP_SERVER_PORT=3000                    # Port for MCP server
MCP_INSPECTOR_PORT=3001                 # Port for debugging interface
LOG_LEVEL=info                          # Logging level (error, warn, info, debug)

# Database Configuration
DATABASE_URL=sqlite:./data/discord_mcp.db  # Database connection string

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100             # Max requests per window
RATE_LIMIT_WINDOW_MS=60000              # Rate limit window (milliseconds)

# Security
CORS_ORIGIN=http://localhost:3001       # Allowed CORS origins
MCP_INSPECTOR_ENABLED=true              # Enable/disable inspector
```

### Discord Bot Permissions Explained

| Permission | Purpose | Required For |
|------------|---------|--------------|
| View Channels | See channels in server | All operations |
| Send Messages | Send messages to channels | `send_message` tool |
| Read Message History | Read past messages | `get_messages`, `search_messages` |
| Manage Messages | Delete messages | `moderate_content` (delete) |
| Embed Links | Send rich embeds | `send_message` with embeds |
| Kick Members | Remove users temporarily | `moderate_content` (kick) |
| Ban Members | Remove users permanently | `moderate_content` (ban) |
| Moderate Members | Timeout users | `moderate_content` (timeout) |

### Security Best Practices

#### 1. API Key Generation

Generate secure API keys:

```bash
# Using Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

# Using OpenSSL
openssl rand -hex 32

# Using Python
python3 -c "import secrets; print(secrets.token_hex(32))"
```

#### 2. Environment Security

```bash
# Set proper file permissions (Unix/Linux/macOS)
chmod 600 .env
chmod 700 data/
chmod 700 logs/

# Never commit .env to version control
echo ".env" >> .gitignore
```

#### 3. Production Security

For production deployments:

```env
NODE_ENV=production
LOG_LEVEL=warn
MCP_INSPECTOR_ENABLED=false
DATABASE_URL=postgresql://user:password@host:port/database
```

### Claude Desktop Integration

To use with Claude Desktop, add to your configuration:

#### macOS/Linux
Edit `~/.config/claude-desktop/config.json`:

```json
{
  "mcpServers": {
    "discord": {
      "command": "node",
      "args": ["/path/to/discord-mcp-server/dist/index.js"],
      "env": {
        "DISCORD_BOT_TOKEN": "your_bot_token",
        "DISCORD_APPLICATION_ID": "your_app_id",
        "JWT_SECRET": "your_jwt_secret",
        "API_KEYS": "your_api_key:your_tenant",
        "ENCRYPTION_KEY": "your_encryption_key"
      }
    }
  }
}
```

#### Windows
Edit `%APPDATA%\Claude\config.json` with the same format.

### Validation and Testing

#### 1. Environment Validation

```bash
# Validate your setup
npm run validate-env

# Generate detailed environment report
npm run env-report
```

#### 2. Connection Testing

```bash
# Test Discord bot connection
npm run test-discord

# Test MCP server
npm run test-mcp

# Run all tests
npm test
```

#### 3. Manual Testing

Test each tool manually:

```bash
# Test send_message
curl -X POST http://localhost:3000/api/test \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "send_message",
    "args": {
      "channel_id": "your_channel_id",
      "content": "Test message from MCP server!"
    },
    "apiKey": "your_api_key"
  }'
```

### Troubleshooting Common Issues

#### Bot Not Responding

1. **Check bot token**: Ensure it's correct and not expired
2. **Verify permissions**: Bot needs proper permissions in Discord server
3. **Check intents**: Enable Message Content Intent in Discord portal
4. **Confirm bot is online**: Should show as online in Discord

#### Authentication Errors

1. **API key format**: Ensure format is `key:tenant` in environment
2. **JWT secret length**: Must be at least 32 characters
3. **Encryption key length**: Must be exactly 32 characters

#### Database Issues

1. **File permissions**: Ensure data directory is writable
2. **SQLite installation**: Verify SQLite3 is properly installed
3. **Database path**: Check DATABASE_URL path is correct

#### Network Issues

1. **Port conflicts**: Ensure ports 3000 and 3001 are available
2. **Firewall**: Check firewall isn't blocking the ports
3. **CORS errors**: Verify CORS_ORIGIN setting

### Development Workflow

#### 1. Development Mode

```bash
# Start with auto-reload
npm run dev

# Start with debugging
DEBUG=* npm run dev

# Start with inspector
npm run inspector
```

#### 2. Code Quality

```bash
# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Run tests with coverage
npm run test:coverage
```

#### 3. Building for Production

```bash
# Build TypeScript
npm run build

# Test production build
npm start

# Verify build works
npm run test:prod
```

### Next Steps

After successful setup:

1. **Read the API Documentation**: `/docs/API.md`
2. **Explore MCP Inspector**: `http://localhost:3001`
3. **Test with Claude Desktop**: Configure and test integration
4. **Review Security**: Check `/docs/SECURITY.md`
5. **Plan Deployment**: See `/docs/DEPLOYMENT.md`

### Getting Help

- **Documentation**: Check `/docs` folder for detailed guides
- **Issues**: Report problems via GitHub issues
- **Logs**: Check `logs/` directory for error details
- **Inspector**: Use MCP Inspector for real-time debugging
